{"name": "fundr-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "vitest"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.542.0", "next": "15.5.2", "radix-ui": "^1.4.3", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^5.0.1", "eslint": "^9", "eslint-config-next": "15.5.2", "jsdom": "^26.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}